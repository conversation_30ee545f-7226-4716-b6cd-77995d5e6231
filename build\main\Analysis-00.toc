(['D:\\Desk\\pikoudai\\main.py'],
 ['D:\\Desk\\pikoudai'],
 [],
 [('D:\\miniconda3\\Lib\\site-packages\\numpy\\_pyinstaller', 0),
  ('D:\\miniconda3\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\miniconda3\\Lib\\site-packages\\_pyinstaller_hooks_contrib', -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.13.5 | packaged by Anaconda, Inc. | (main, Jun 12 2025, 16:37:03) [MSC '
 'v.1929 64 bit (AMD64)]',
 [('pyi_rth_inspect',
   'D:\\miniconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'D:\\miniconda3\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('main', 'D:\\Desk\\pikoudai\\main.py', 'PYSOURCE')],
 [('zipfile', 'D:\\miniconda3\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'D:\\miniconda3\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\miniconda3\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('pathlib', 'D:\\miniconda3\\Lib\\pathlib\\__init__.py', 'PYMODULE'),
  ('pathlib._local', 'D:\\miniconda3\\Lib\\pathlib\\_local.py', 'PYMODULE'),
  ('glob', 'D:\\miniconda3\\Lib\\glob.py', 'PYMODULE'),
  ('fnmatch', 'D:\\miniconda3\\Lib\\fnmatch.py', 'PYMODULE'),
  ('pathlib._abc', 'D:\\miniconda3\\Lib\\pathlib\\_abc.py', 'PYMODULE'),
  ('contextlib', 'D:\\miniconda3\\Lib\\contextlib.py', 'PYMODULE'),
  ('argparse', 'D:\\miniconda3\\Lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'D:\\miniconda3\\Lib\\textwrap.py', 'PYMODULE'),
  ('copy', 'D:\\miniconda3\\Lib\\copy.py', 'PYMODULE'),
  ('gettext', 'D:\\miniconda3\\Lib\\gettext.py', 'PYMODULE'),
  ('py_compile', 'D:\\miniconda3\\Lib\\py_compile.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\miniconda3\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib', 'D:\\miniconda3\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\miniconda3\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\miniconda3\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\miniconda3\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('csv', 'D:\\miniconda3\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\miniconda3\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\miniconda3\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message', 'D:\\miniconda3\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'D:\\miniconda3\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\miniconda3\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime', 'D:\\miniconda3\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('string', 'D:\\miniconda3\\Lib\\string.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\miniconda3\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\miniconda3\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib', 'D:\\miniconda3\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('email.iterators', 'D:\\miniconda3\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.generator', 'D:\\miniconda3\\Lib\\email\\generator.py', 'PYMODULE'),
  ('random', 'D:\\miniconda3\\Lib\\random.py', 'PYMODULE'),
  ('statistics', 'D:\\miniconda3\\Lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'D:\\miniconda3\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\miniconda3\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'D:\\miniconda3\\Lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'D:\\miniconda3\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'D:\\miniconda3\\Lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'D:\\miniconda3\\Lib\\hashlib.py', 'PYMODULE'),
  ('logging', 'D:\\miniconda3\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('pickle', 'D:\\miniconda3\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\miniconda3\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'D:\\miniconda3\\Lib\\dataclasses.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\miniconda3\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('bisect', 'D:\\miniconda3\\Lib\\bisect.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\miniconda3\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'D:\\miniconda3\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'D:\\miniconda3\\Lib\\getopt.py', 'PYMODULE'),
  ('email.charset', 'D:\\miniconda3\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'D:\\miniconda3\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.base64mime', 'D:\\miniconda3\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email._policybase',
   'D:\\miniconda3\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header', 'D:\\miniconda3\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.errors', 'D:\\miniconda3\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.utils', 'D:\\miniconda3\\Lib\\email\\utils.py', 'PYMODULE'),
  ('socket', 'D:\\miniconda3\\Lib\\socket.py', 'PYMODULE'),
  ('selectors', 'D:\\miniconda3\\Lib\\selectors.py', 'PYMODULE'),
  ('email._parseaddr', 'D:\\miniconda3\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('calendar', 'D:\\miniconda3\\Lib\\calendar.py', 'PYMODULE'),
  ('quopri', 'D:\\miniconda3\\Lib\\quopri.py', 'PYMODULE'),
  ('importlib.abc', 'D:\\miniconda3\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\miniconda3\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\miniconda3\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'D:\\miniconda3\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\miniconda3\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\miniconda3\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile', 'D:\\miniconda3\\Lib\\tempfile.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\miniconda3\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\miniconda3\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\miniconda3\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\miniconda3\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\miniconda3\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('email', 'D:\\miniconda3\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'D:\\miniconda3\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\miniconda3\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('__future__', 'D:\\miniconda3\\Lib\\__future__.py', 'PYMODULE'),
  ('importlib.readers',
   'D:\\miniconda3\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\miniconda3\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\miniconda3\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('tokenize', 'D:\\miniconda3\\Lib\\tokenize.py', 'PYMODULE'),
  ('token', 'D:\\miniconda3\\Lib\\token.py', 'PYMODULE'),
  ('lzma', 'D:\\miniconda3\\Lib\\lzma.py', 'PYMODULE'),
  ('_compression', 'D:\\miniconda3\\Lib\\_compression.py', 'PYMODULE'),
  ('bz2', 'D:\\miniconda3\\Lib\\bz2.py', 'PYMODULE'),
  ('threading', 'D:\\miniconda3\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local', 'D:\\miniconda3\\Lib\\_threading_local.py', 'PYMODULE'),
  ('struct', 'D:\\miniconda3\\Lib\\struct.py', 'PYMODULE'),
  ('shutil', 'D:\\miniconda3\\Lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'D:\\miniconda3\\Lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'D:\\miniconda3\\Lib\\gzip.py', 'PYMODULE'),
  ('importlib.util', 'D:\\miniconda3\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'D:\\miniconda3\\Lib\\inspect.py', 'PYMODULE'),
  ('dis', 'D:\\miniconda3\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\miniconda3\\Lib\\opcode.py', 'PYMODULE'),
  ('_opcode_metadata', 'D:\\miniconda3\\Lib\\_opcode_metadata.py', 'PYMODULE'),
  ('ast', 'D:\\miniconda3\\Lib\\ast.py', 'PYMODULE'),
  ('_colorize', 'D:\\miniconda3\\Lib\\_colorize.py', 'PYMODULE'),
  ('stringprep', 'D:\\miniconda3\\Lib\\stringprep.py', 'PYMODULE'),
  ('_py_abc', 'D:\\miniconda3\\Lib\\_py_abc.py', 'PYMODULE'),
  ('subprocess', 'D:\\miniconda3\\Lib\\subprocess.py', 'PYMODULE'),
  ('signal', 'D:\\miniconda3\\Lib\\signal.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\miniconda3\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('typing', 'D:\\miniconda3\\Lib\\typing.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\miniconda3\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('ipaddress', 'D:\\miniconda3\\Lib\\ipaddress.py', 'PYMODULE'),
  ('datetime', 'D:\\miniconda3\\Lib\\datetime.py', 'PYMODULE'),
  ('_pydatetime', 'D:\\miniconda3\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_strptime', 'D:\\miniconda3\\Lib\\_strptime.py', 'PYMODULE'),
  ('json', 'D:\\miniconda3\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'D:\\miniconda3\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'D:\\miniconda3\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\miniconda3\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('requests',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('urllib.request', 'D:\\miniconda3\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('getpass', 'D:\\miniconda3\\Lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'D:\\miniconda3\\Lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'D:\\miniconda3\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'D:\\miniconda3\\Lib\\netrc.py', 'PYMODULE'),
  ('mimetypes', 'D:\\miniconda3\\Lib\\mimetypes.py', 'PYMODULE'),
  ('urllib.response', 'D:\\miniconda3\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib.error', 'D:\\miniconda3\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('http.client', 'D:\\miniconda3\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookies', 'D:\\miniconda3\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('http.cookiejar', 'D:\\miniconda3\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http', 'D:\\miniconda3\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('requests.models',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('idna', 'D:\\miniconda3\\Lib\\site-packages\\idna\\__init__.py', 'PYMODULE'),
  ('idna.intranges',
   'D:\\miniconda3\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core',
   'D:\\miniconda3\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\miniconda3\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\miniconda3\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\miniconda3\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('typing_extensions',
   'D:\\miniconda3\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('hmac', 'D:\\miniconda3\\Lib\\hmac.py', 'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.http2',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('zstandard',
   'D:\\miniconda3\\Lib\\site-packages\\zstandard\\__init__.py',
   'PYMODULE'),
  ('zstandard.backend_cffi',
   'D:\\miniconda3\\Lib\\site-packages\\zstandard\\backend_cffi.py',
   'PYMODULE'),
  ('platform', 'D:\\miniconda3\\Lib\\platform.py', 'PYMODULE'),
  ('ctypes', 'D:\\miniconda3\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes.util', 'D:\\miniconda3\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes._aix', 'D:\\miniconda3\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\miniconda3\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\miniconda3\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\miniconda3\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\miniconda3\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes._endian', 'D:\\miniconda3\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('_ios_support', 'D:\\miniconda3\\Lib\\_ios_support.py', 'PYMODULE'),
  ('brotli', 'D:\\miniconda3\\Lib\\site-packages\\brotli.py', 'PYMODULE'),
  ('urllib3._base_connection',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('queue', 'D:\\miniconda3\\Lib\\queue.py', 'PYMODULE'),
  ('urllib3.util.response',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('socks', 'D:\\miniconda3\\Lib\\site-packages\\socks.py', 'PYMODULE'),
  ('win_inet_pton',
   'D:\\miniconda3\\Lib\\site-packages\\win_inet_pton.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'D:\\miniconda3\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\miniconda3\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('cryptography',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('bcrypt',
   'D:\\miniconda3\\Lib\\site-packages\\bcrypt\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.utils',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('ssl', 'D:\\miniconda3\\Lib\\ssl.py', 'PYMODULE'),
  ('charset_normalizer',
   'D:\\miniconda3\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\miniconda3\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\miniconda3\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\miniconda3\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\miniconda3\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\miniconda3\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.md',
   'D:\\miniconda3\\Lib\\site-packages\\charset_normalizer\\md.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\miniconda3\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\miniconda3\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('urllib3',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE')],
 [('python313.dll', 'D:\\miniconda3\\python313.dll', 'BINARY'),
  ('_decimal.pyd', 'D:\\miniconda3\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\miniconda3\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('select.pyd', 'D:\\miniconda3\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\miniconda3\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\miniconda3\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\miniconda3\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'D:\\miniconda3\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('zstandard\\_cffi.cp313-win_amd64.pyd',
   'D:\\miniconda3\\Lib\\site-packages\\zstandard\\_cffi.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('zstandard\\backend_c.cp313-win_amd64.pyd',
   'D:\\miniconda3\\Lib\\site-packages\\zstandard\\backend_c.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_ctypes.pyd', 'D:\\miniconda3\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_wmi.pyd', 'D:\\miniconda3\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('_brotli.cp313-win_amd64.pyd',
   'D:\\miniconda3\\Lib\\site-packages\\_brotli.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_queue.pyd', 'D:\\miniconda3\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_cffi_backend.cp313-win_amd64.pyd',
   'D:\\miniconda3\\Lib\\site-packages\\_cffi_backend.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('bcrypt\\_bcrypt.pyd',
   'D:\\miniconda3\\Lib\\site-packages\\bcrypt\\_bcrypt.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'D:\\miniconda3\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('zlib.dll', 'D:\\miniconda3\\zlib.dll', 'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'D:\\miniconda3\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('libmpdec-4.dll', 'D:\\miniconda3\\Library\\bin\\libmpdec-4.dll', 'BINARY'),
  ('libcrypto-3-x64.dll',
   'D:\\miniconda3\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('liblzma.dll', 'D:\\miniconda3\\Library\\bin\\liblzma.dll', 'BINARY'),
  ('LIBBZ2.dll', 'D:\\miniconda3\\Library\\bin\\LIBBZ2.dll', 'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('python3.dll', 'D:\\miniconda3\\python3.dll', 'BINARY'),
  ('zstd.dll', 'D:\\miniconda3\\Library\\bin\\zstd.dll', 'BINARY'),
  ('ffi.dll', 'D:\\miniconda3\\Library\\bin\\ffi.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll', 'D:\\miniconda3\\VCRUNTIME140_1.dll', 'BINARY'),
  ('MSVCP140.dll', 'D:\\miniconda3\\MSVCP140.dll', 'BINARY'),
  ('libssl-3-x64.dll',
   'D:\\miniconda3\\Library\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\miniconda3\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('ucrtbase.dll', 'D:\\miniconda3\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\miniconda3\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\miniconda3\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\miniconda3\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\miniconda3\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\miniconda3\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('certifi\\cacert.pem',
   'D:\\miniconda3\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\miniconda3\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\METADATA',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\METADATA',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\RECORD',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\RECORD',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\WHEEL',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE.BSD',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE.APACHE',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\INSTALLER',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\direct_url.json',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\direct_url.json',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\REQUESTED',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\REQUESTED',
   'DATA'),
  ('base_library.zip',
   'D:\\Desk\\pikoudai\\build\\main\\base_library.zip',
   'DATA')],
 [('copyreg', 'D:\\miniconda3\\Lib\\copyreg.py', 'PYMODULE'),
  ('keyword', 'D:\\miniconda3\\Lib\\keyword.py', 'PYMODULE'),
  ('traceback', 'D:\\miniconda3\\Lib\\traceback.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'D:\\miniconda3\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'D:\\miniconda3\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'D:\\miniconda3\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8', 'D:\\miniconda3\\Lib\\encodings\\utf_8.py', 'PYMODULE'),
  ('encodings.utf_7', 'D:\\miniconda3\\Lib\\encodings\\utf_7.py', 'PYMODULE'),
  ('encodings.utf_32_le',
   'D:\\miniconda3\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'D:\\miniconda3\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32', 'D:\\miniconda3\\Lib\\encodings\\utf_32.py', 'PYMODULE'),
  ('encodings.utf_16_le',
   'D:\\miniconda3\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'D:\\miniconda3\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16', 'D:\\miniconda3\\Lib\\encodings\\utf_16.py', 'PYMODULE'),
  ('encodings.unicode_escape',
   'D:\\miniconda3\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'D:\\miniconda3\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'D:\\miniconda3\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'D:\\miniconda3\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'D:\\miniconda3\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'D:\\miniconda3\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13', 'D:\\miniconda3\\Lib\\encodings\\rot_13.py', 'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'D:\\miniconda3\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'D:\\miniconda3\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'D:\\miniconda3\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'D:\\miniconda3\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos', 'D:\\miniconda3\\Lib\\encodings\\palmos.py', 'PYMODULE'),
  ('encodings.oem', 'D:\\miniconda3\\Lib\\encodings\\oem.py', 'PYMODULE'),
  ('encodings.mbcs', 'D:\\miniconda3\\Lib\\encodings\\mbcs.py', 'PYMODULE'),
  ('encodings.mac_turkish',
   'D:\\miniconda3\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'D:\\miniconda3\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'D:\\miniconda3\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'D:\\miniconda3\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'D:\\miniconda3\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'D:\\miniconda3\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'D:\\miniconda3\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'D:\\miniconda3\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'D:\\miniconda3\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'D:\\miniconda3\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'D:\\miniconda3\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048', 'D:\\miniconda3\\Lib\\encodings\\kz1048.py', 'PYMODULE'),
  ('encodings.koi8_u', 'D:\\miniconda3\\Lib\\encodings\\koi8_u.py', 'PYMODULE'),
  ('encodings.koi8_t', 'D:\\miniconda3\\Lib\\encodings\\koi8_t.py', 'PYMODULE'),
  ('encodings.koi8_r', 'D:\\miniconda3\\Lib\\encodings\\koi8_r.py', 'PYMODULE'),
  ('encodings.johab', 'D:\\miniconda3\\Lib\\encodings\\johab.py', 'PYMODULE'),
  ('encodings.iso8859_9',
   'D:\\miniconda3\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'D:\\miniconda3\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'D:\\miniconda3\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'D:\\miniconda3\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'D:\\miniconda3\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'D:\\miniconda3\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'D:\\miniconda3\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'D:\\miniconda3\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'D:\\miniconda3\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'D:\\miniconda3\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'D:\\miniconda3\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'D:\\miniconda3\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'D:\\miniconda3\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'D:\\miniconda3\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'D:\\miniconda3\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'D:\\miniconda3\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'D:\\miniconda3\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'D:\\miniconda3\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'D:\\miniconda3\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'D:\\miniconda3\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'D:\\miniconda3\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'D:\\miniconda3\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna', 'D:\\miniconda3\\Lib\\encodings\\idna.py', 'PYMODULE'),
  ('encodings.hz', 'D:\\miniconda3\\Lib\\encodings\\hz.py', 'PYMODULE'),
  ('encodings.hp_roman8',
   'D:\\miniconda3\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'D:\\miniconda3\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk', 'D:\\miniconda3\\Lib\\encodings\\gbk.py', 'PYMODULE'),
  ('encodings.gb2312', 'D:\\miniconda3\\Lib\\encodings\\gb2312.py', 'PYMODULE'),
  ('encodings.gb18030',
   'D:\\miniconda3\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr', 'D:\\miniconda3\\Lib\\encodings\\euc_kr.py', 'PYMODULE'),
  ('encodings.euc_jp', 'D:\\miniconda3\\Lib\\encodings\\euc_jp.py', 'PYMODULE'),
  ('encodings.euc_jisx0213',
   'D:\\miniconda3\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'D:\\miniconda3\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950', 'D:\\miniconda3\\Lib\\encodings\\cp950.py', 'PYMODULE'),
  ('encodings.cp949', 'D:\\miniconda3\\Lib\\encodings\\cp949.py', 'PYMODULE'),
  ('encodings.cp932', 'D:\\miniconda3\\Lib\\encodings\\cp932.py', 'PYMODULE'),
  ('encodings.cp875', 'D:\\miniconda3\\Lib\\encodings\\cp875.py', 'PYMODULE'),
  ('encodings.cp874', 'D:\\miniconda3\\Lib\\encodings\\cp874.py', 'PYMODULE'),
  ('encodings.cp869', 'D:\\miniconda3\\Lib\\encodings\\cp869.py', 'PYMODULE'),
  ('encodings.cp866', 'D:\\miniconda3\\Lib\\encodings\\cp866.py', 'PYMODULE'),
  ('encodings.cp865', 'D:\\miniconda3\\Lib\\encodings\\cp865.py', 'PYMODULE'),
  ('encodings.cp864', 'D:\\miniconda3\\Lib\\encodings\\cp864.py', 'PYMODULE'),
  ('encodings.cp863', 'D:\\miniconda3\\Lib\\encodings\\cp863.py', 'PYMODULE'),
  ('encodings.cp862', 'D:\\miniconda3\\Lib\\encodings\\cp862.py', 'PYMODULE'),
  ('encodings.cp861', 'D:\\miniconda3\\Lib\\encodings\\cp861.py', 'PYMODULE'),
  ('encodings.cp860', 'D:\\miniconda3\\Lib\\encodings\\cp860.py', 'PYMODULE'),
  ('encodings.cp858', 'D:\\miniconda3\\Lib\\encodings\\cp858.py', 'PYMODULE'),
  ('encodings.cp857', 'D:\\miniconda3\\Lib\\encodings\\cp857.py', 'PYMODULE'),
  ('encodings.cp856', 'D:\\miniconda3\\Lib\\encodings\\cp856.py', 'PYMODULE'),
  ('encodings.cp855', 'D:\\miniconda3\\Lib\\encodings\\cp855.py', 'PYMODULE'),
  ('encodings.cp852', 'D:\\miniconda3\\Lib\\encodings\\cp852.py', 'PYMODULE'),
  ('encodings.cp850', 'D:\\miniconda3\\Lib\\encodings\\cp850.py', 'PYMODULE'),
  ('encodings.cp775', 'D:\\miniconda3\\Lib\\encodings\\cp775.py', 'PYMODULE'),
  ('encodings.cp737', 'D:\\miniconda3\\Lib\\encodings\\cp737.py', 'PYMODULE'),
  ('encodings.cp720', 'D:\\miniconda3\\Lib\\encodings\\cp720.py', 'PYMODULE'),
  ('encodings.cp500', 'D:\\miniconda3\\Lib\\encodings\\cp500.py', 'PYMODULE'),
  ('encodings.cp437', 'D:\\miniconda3\\Lib\\encodings\\cp437.py', 'PYMODULE'),
  ('encodings.cp424', 'D:\\miniconda3\\Lib\\encodings\\cp424.py', 'PYMODULE'),
  ('encodings.cp273', 'D:\\miniconda3\\Lib\\encodings\\cp273.py', 'PYMODULE'),
  ('encodings.cp1258', 'D:\\miniconda3\\Lib\\encodings\\cp1258.py', 'PYMODULE'),
  ('encodings.cp1257', 'D:\\miniconda3\\Lib\\encodings\\cp1257.py', 'PYMODULE'),
  ('encodings.cp1256', 'D:\\miniconda3\\Lib\\encodings\\cp1256.py', 'PYMODULE'),
  ('encodings.cp1255', 'D:\\miniconda3\\Lib\\encodings\\cp1255.py', 'PYMODULE'),
  ('encodings.cp1254', 'D:\\miniconda3\\Lib\\encodings\\cp1254.py', 'PYMODULE'),
  ('encodings.cp1253', 'D:\\miniconda3\\Lib\\encodings\\cp1253.py', 'PYMODULE'),
  ('encodings.cp1252', 'D:\\miniconda3\\Lib\\encodings\\cp1252.py', 'PYMODULE'),
  ('encodings.cp1251', 'D:\\miniconda3\\Lib\\encodings\\cp1251.py', 'PYMODULE'),
  ('encodings.cp1250', 'D:\\miniconda3\\Lib\\encodings\\cp1250.py', 'PYMODULE'),
  ('encodings.cp1140', 'D:\\miniconda3\\Lib\\encodings\\cp1140.py', 'PYMODULE'),
  ('encodings.cp1125', 'D:\\miniconda3\\Lib\\encodings\\cp1125.py', 'PYMODULE'),
  ('encodings.cp1026', 'D:\\miniconda3\\Lib\\encodings\\cp1026.py', 'PYMODULE'),
  ('encodings.cp1006', 'D:\\miniconda3\\Lib\\encodings\\cp1006.py', 'PYMODULE'),
  ('encodings.cp037', 'D:\\miniconda3\\Lib\\encodings\\cp037.py', 'PYMODULE'),
  ('encodings.charmap',
   'D:\\miniconda3\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'D:\\miniconda3\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'D:\\miniconda3\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5', 'D:\\miniconda3\\Lib\\encodings\\big5.py', 'PYMODULE'),
  ('encodings.base64_codec',
   'D:\\miniconda3\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii', 'D:\\miniconda3\\Lib\\encodings\\ascii.py', 'PYMODULE'),
  ('encodings.aliases',
   'D:\\miniconda3\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings', 'D:\\miniconda3\\Lib\\encodings\\__init__.py', 'PYMODULE'),
  ('abc', 'D:\\miniconda3\\Lib\\abc.py', 'PYMODULE'),
  ('posixpath', 'D:\\miniconda3\\Lib\\posixpath.py', 'PYMODULE'),
  ('io', 'D:\\miniconda3\\Lib\\io.py', 'PYMODULE'),
  ('stat', 'D:\\miniconda3\\Lib\\stat.py', 'PYMODULE'),
  ('types', 'D:\\miniconda3\\Lib\\types.py', 'PYMODULE'),
  ('collections', 'D:\\miniconda3\\Lib\\collections\\__init__.py', 'PYMODULE'),
  ('linecache', 'D:\\miniconda3\\Lib\\linecache.py', 'PYMODULE'),
  ('re._parser', 'D:\\miniconda3\\Lib\\re\\_parser.py', 'PYMODULE'),
  ('re._constants', 'D:\\miniconda3\\Lib\\re\\_constants.py', 'PYMODULE'),
  ('re._compiler', 'D:\\miniconda3\\Lib\\re\\_compiler.py', 'PYMODULE'),
  ('re._casefix', 'D:\\miniconda3\\Lib\\re\\_casefix.py', 'PYMODULE'),
  ('re', 'D:\\miniconda3\\Lib\\re\\__init__.py', 'PYMODULE'),
  ('codecs', 'D:\\miniconda3\\Lib\\codecs.py', 'PYMODULE'),
  ('locale', 'D:\\miniconda3\\Lib\\locale.py', 'PYMODULE'),
  ('ntpath', 'D:\\miniconda3\\Lib\\ntpath.py', 'PYMODULE'),
  ('functools', 'D:\\miniconda3\\Lib\\functools.py', 'PYMODULE'),
  ('heapq', 'D:\\miniconda3\\Lib\\heapq.py', 'PYMODULE'),
  ('weakref', 'D:\\miniconda3\\Lib\\weakref.py', 'PYMODULE'),
  ('operator', 'D:\\miniconda3\\Lib\\operator.py', 'PYMODULE'),
  ('sre_compile', 'D:\\miniconda3\\Lib\\sre_compile.py', 'PYMODULE'),
  ('genericpath', 'D:\\miniconda3\\Lib\\genericpath.py', 'PYMODULE'),
  ('reprlib', 'D:\\miniconda3\\Lib\\reprlib.py', 'PYMODULE'),
  ('sre_parse', 'D:\\miniconda3\\Lib\\sre_parse.py', 'PYMODULE'),
  ('_weakrefset', 'D:\\miniconda3\\Lib\\_weakrefset.py', 'PYMODULE'),
  ('_collections_abc', 'D:\\miniconda3\\Lib\\_collections_abc.py', 'PYMODULE'),
  ('os', 'D:\\miniconda3\\Lib\\os.py', 'PYMODULE'),
  ('enum', 'D:\\miniconda3\\Lib\\enum.py', 'PYMODULE'),
  ('sre_constants', 'D:\\miniconda3\\Lib\\sre_constants.py', 'PYMODULE'),
  ('warnings', 'D:\\miniconda3\\Lib\\warnings.py', 'PYMODULE')])
