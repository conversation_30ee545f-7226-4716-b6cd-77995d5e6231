('D:\\Desk\\pikoudai\\build\\main\\PYZ-00.pyz',
 [('__future__', 'D:\\miniconda3\\Lib\\__future__.py', 'PYMODULE'),
  ('_colorize', 'D:\\miniconda3\\Lib\\_colorize.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\miniconda3\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'D:\\miniconda3\\Lib\\_compression.py', 'PYMODULE'),
  ('_ios_support', 'D:\\miniconda3\\Lib\\_ios_support.py', 'PYMODULE'),
  ('_opcode_metadata', 'D:\\miniconda3\\Lib\\_opcode_metadata.py', 'PYMODULE'),
  ('_py_abc', 'D:\\miniconda3\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'D:\\miniconda3\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\miniconda3\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_strptime', 'D:\\miniconda3\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'D:\\miniconda3\\Lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'D:\\miniconda3\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\miniconda3\\Lib\\ast.py', 'PYMODULE'),
  ('base64', 'D:\\miniconda3\\Lib\\base64.py', 'PYMODULE'),
  ('bcrypt',
   'D:\\miniconda3\\Lib\\site-packages\\bcrypt\\__init__.py',
   'PYMODULE'),
  ('bisect', 'D:\\miniconda3\\Lib\\bisect.py', 'PYMODULE'),
  ('brotli', 'D:\\miniconda3\\Lib\\site-packages\\brotli.py', 'PYMODULE'),
  ('bz2', 'D:\\miniconda3\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\miniconda3\\Lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'D:\\miniconda3\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\miniconda3\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\miniconda3\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\miniconda3\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\miniconda3\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\miniconda3\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\miniconda3\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.md',
   'D:\\miniconda3\\Lib\\site-packages\\charset_normalizer\\md.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\miniconda3\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\miniconda3\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\miniconda3\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('contextlib', 'D:\\miniconda3\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\miniconda3\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\miniconda3\\Lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'D:\\miniconda3\\Lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('csv', 'D:\\miniconda3\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'D:\\miniconda3\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'D:\\miniconda3\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian', 'D:\\miniconda3\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('ctypes.macholib',
   'D:\\miniconda3\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\miniconda3\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\miniconda3\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\miniconda3\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'D:\\miniconda3\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('dataclasses', 'D:\\miniconda3\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\miniconda3\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\miniconda3\\Lib\\decimal.py', 'PYMODULE'),
  ('dis', 'D:\\miniconda3\\Lib\\dis.py', 'PYMODULE'),
  ('email', 'D:\\miniconda3\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\miniconda3\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\miniconda3\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'D:\\miniconda3\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase',
   'D:\\miniconda3\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime', 'D:\\miniconda3\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'D:\\miniconda3\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\miniconda3\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'D:\\miniconda3\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'D:\\miniconda3\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\miniconda3\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'D:\\miniconda3\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'D:\\miniconda3\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\miniconda3\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'D:\\miniconda3\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'D:\\miniconda3\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'D:\\miniconda3\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'D:\\miniconda3\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'D:\\miniconda3\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'D:\\miniconda3\\Lib\\email\\utils.py', 'PYMODULE'),
  ('fnmatch', 'D:\\miniconda3\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\miniconda3\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'D:\\miniconda3\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\miniconda3\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\miniconda3\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\miniconda3\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\miniconda3\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\miniconda3\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\miniconda3\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\miniconda3\\Lib\\hmac.py', 'PYMODULE'),
  ('http', 'D:\\miniconda3\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'D:\\miniconda3\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'D:\\miniconda3\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.cookies', 'D:\\miniconda3\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('idna', 'D:\\miniconda3\\Lib\\site-packages\\idna\\__init__.py', 'PYMODULE'),
  ('idna.core',
   'D:\\miniconda3\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\miniconda3\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\miniconda3\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\miniconda3\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\miniconda3\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib', 'D:\\miniconda3\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\miniconda3\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\miniconda3\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\miniconda3\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'D:\\miniconda3\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\miniconda3\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\miniconda3\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\miniconda3\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\miniconda3\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\miniconda3\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\miniconda3\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\miniconda3\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\miniconda3\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\miniconda3\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\miniconda3\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\miniconda3\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\miniconda3\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'D:\\miniconda3\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\miniconda3\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\miniconda3\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\miniconda3\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'D:\\miniconda3\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'D:\\miniconda3\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\miniconda3\\Lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'D:\\miniconda3\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'D:\\miniconda3\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'D:\\miniconda3\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\miniconda3\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'D:\\miniconda3\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'D:\\miniconda3\\Lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'D:\\miniconda3\\Lib\\mimetypes.py', 'PYMODULE'),
  ('netrc', 'D:\\miniconda3\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'D:\\miniconda3\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'D:\\miniconda3\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\miniconda3\\Lib\\opcode.py', 'PYMODULE'),
  ('pathlib', 'D:\\miniconda3\\Lib\\pathlib\\__init__.py', 'PYMODULE'),
  ('pathlib._abc', 'D:\\miniconda3\\Lib\\pathlib\\_abc.py', 'PYMODULE'),
  ('pathlib._local', 'D:\\miniconda3\\Lib\\pathlib\\_local.py', 'PYMODULE'),
  ('pickle', 'D:\\miniconda3\\Lib\\pickle.py', 'PYMODULE'),
  ('platform', 'D:\\miniconda3\\Lib\\platform.py', 'PYMODULE'),
  ('pprint', 'D:\\miniconda3\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'D:\\miniconda3\\Lib\\py_compile.py', 'PYMODULE'),
  ('queue', 'D:\\miniconda3\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\miniconda3\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\miniconda3\\Lib\\random.py', 'PYMODULE'),
  ('requests',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\miniconda3\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('selectors', 'D:\\miniconda3\\Lib\\selectors.py', 'PYMODULE'),
  ('shutil', 'D:\\miniconda3\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\miniconda3\\Lib\\signal.py', 'PYMODULE'),
  ('socket', 'D:\\miniconda3\\Lib\\socket.py', 'PYMODULE'),
  ('socks', 'D:\\miniconda3\\Lib\\site-packages\\socks.py', 'PYMODULE'),
  ('ssl', 'D:\\miniconda3\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'D:\\miniconda3\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\miniconda3\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\miniconda3\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\miniconda3\\Lib\\subprocess.py', 'PYMODULE'),
  ('tarfile', 'D:\\miniconda3\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\miniconda3\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\miniconda3\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\miniconda3\\Lib\\threading.py', 'PYMODULE'),
  ('token', 'D:\\miniconda3\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\miniconda3\\Lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\miniconda3\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('typing', 'D:\\miniconda3\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'D:\\miniconda3\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('urllib', 'D:\\miniconda3\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'D:\\miniconda3\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\miniconda3\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request', 'D:\\miniconda3\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib.response', 'D:\\miniconda3\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib3',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\miniconda3\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('win_inet_pton',
   'D:\\miniconda3\\Lib\\site-packages\\win_inet_pton.py',
   'PYMODULE'),
  ('zipfile', 'D:\\miniconda3\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'D:\\miniconda3\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\miniconda3\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zstandard',
   'D:\\miniconda3\\Lib\\site-packages\\zstandard\\__init__.py',
   'PYMODULE'),
  ('zstandard.backend_cffi',
   'D:\\miniconda3\\Lib\\site-packages\\zstandard\\backend_cffi.py',
   'PYMODULE')])
